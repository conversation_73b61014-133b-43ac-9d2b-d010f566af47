# Currents API MCP Server Dependencies
# Core MCP framework
fastmcp>=2.0.0

# HTTP client for API requests
httpx>=0.25.0

# Date parsing utilities
python-dateutil>=2.8.0

# JSON handling (included in Python standard library, listed for completeness)
# json - built-in

# Async support (included in Python 3.11+)
# asyncio - built-in

# Environment variable handling (included in Python standard library)
# os - built-in

# Type hints (included in Python 3.11+)
# typing - built-in

# Optional: For development and testing
# pytest>=7.0.0
# pytest-asyncio>=0.21.0